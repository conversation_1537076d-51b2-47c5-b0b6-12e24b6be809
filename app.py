import streamlit as st
# Temporarily commenting out pandas to fix the numpy import error
# import pandas as pd
import requests
import json
# Commenting out plotly imports since they're causing issues
# import plotly.express as px
# import plotly.graph_objects as go

# st.markdown(
#     """
#     <style>
#     [data-testid="stHeader"] {
#         background-image: src="./mstack logo.png";
#         background-repeat: no-repeat;
#         background-size: 120px;
#         padding-left: 130px; /* Adjust based on logo size */
#     }
#     </style>
#     """,
#     unsafe_allow_html=True
# )

# Set page configuration
st.set_page_config(
    page_title="Chemical Supplier Finder",
    page_icon="🧪",
    layout="wide"
)

# Custom CSS for styling
st.markdown("""
    <style>
    .main {
        padding: 2rem;
    }
    .stApp {
        max-width: 1200px;
        margin: 0 auto;
    }
    .title-container {
        background-color: #f0f2f6;
        padding: 1.5rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .card {
        background-color: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }
    .supplier-card {
        border-left: 4px solid #4CAF50;
    }
    </style>
""", unsafe_allow_html=True)
#import streamlit as st

st.image("mstackLogo.png", width=150)  # Adjust width as needed
st.markdown("---")


def api_get_hs_codes(chemical_name, application, category=None):
    """Get HS codes for a given chemical and its application using external API.

    Args:
        chemical_name (str): The name of the chemical
        application (str): The application of the chemical
        category (str, optional): The category of the chemical

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import json

    # API endpoint for chemical lookup
    api_url = "http://************:8081/api/chemical-lookup"

    # Prepare payload
    payload = {
        "chemical_name": chemical_name.upper()
    }

    # Add application if provided
    if application and application.strip():
        payload["chemical_application"] = application

    # Add category if provided
    if category and category.strip():
        payload["chemical_category"] = category

    # Headers for API request
    headers = {
        "Content-Type": "application/json"
    }

    try:
        # Make API request
        response = requests.post(api_url, headers=headers, json=payload)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()
            print(api_response)

            # Format in expected structure
            if "hs_code" in api_response:
                if "product_family" not in api_response:
                    output= {
                        "status": "success",
                        "data": [
                            {
                                "hs_code": api_response["hs_code"],
                                "product_family": [chemical_name],
                                "description": f"{chemical_name} - {application or 'General'}"
                            }
                        ]
                    }
                    print(f"2a. Partial Success: returning output:{output}")
                    return output
                elif api_response["product_family"] =="Not found":
                    output= {
                        "status": "success",
                        "data": [
                            {
                                "hs_code": api_response["hs_code"],
                                "product_family": [chemical_name],
                                "description": f"{chemical_name} - {application or 'General'}"
                            }
                        ]
                    }
                    print(f"2b. Partial Success:  returning output:{output}")
                    return output
                else:
                    output= {
                        "status": "success",
                        "data": [
                            {
                                "hs_code": api_response["hs_code"],
                                "product_family": api_response["product_family"],
                                "description": f"{chemical_name} - {application or 'General'}"
                            }
                        ]
                    }
                    print(f"1. Complete Success: returning output:{output}")
                    return output
            else:
                output= {
                    "status": "error",
                    "message": "HS code not found in API response"
                }
                print(f"3. returning output:{output}")
                return output
        else:
            # Return error if API call failed
            output= {
                "status": "error",
                "message": f"API request failed with status code: {response.status_code}"
            }
            print(f"4. returning output:{output}")
            return output
    except Exception as e:
        # Handle any exceptions during API call
        output=  {
            "status": "error",
            "message": f"Failed to connect to API: {str(e)}"
        }
        print(f"5. returning output:{output}")
        return output
def api_get_top_countries_tariff(hs_code, chemical_name, destination, months=12):
    """Get top countries exporting a product with given HS code using the actual API.

    Args:
        hs_code (str): The HS code to search for
        chemical_name (str, optional): The name of the chemical
        months (int, optional): Number of months of data to retrieve

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import urllib.parse

    # Format HS code to required format (remove dots if present)
    formatted_hs_code = hs_code.replace(".", "")

    # Build API URL with query parameters
    base_url = "http://************:8080/api/top_suppliers_by_geography_tariff"

    # Prepare query parameters
    params = {
        "hs_code": formatted_hs_code,
        "destination":destination,
        "months": months
    }

    # Add chemical name if provided
    if chemical_name:
        params["chemical_name"] = chemical_name

    # Encode parameters for URL
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"

    try:
        # Make API request
        response = requests.get(api_url)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()

            # Check if API call was successful
            if api_response.get("success") is True and "result" in api_response:
                # Extract the data from result
                api_data = api_response["result"].get("data", [])
                print(api_data)

                # Transform API data to match expected structure in the application
                transformed_data = []

                for item in api_data:
                    # Handle potential missing or None values
                    shipment_count = item.get("shipment_count", 0) or 0
                    total_quantity = item.get("total_quantity", 0) or 0
                    average_fob = item.get("average_fob", 0) or 0

                    # Calculate total value based on average price and total quantity
                    total_value = average_fob * total_quantity if average_fob and total_quantity else 0

                    # Parse tariff_info JSON string if available
                    tariff_info_str = item.get("tariff_info")
                    try:
                        tariff_info = json.loads(tariff_info_str) if tariff_info_str else {}
                    except json.JSONDecodeError:
                        tariff_info = {}

                    # Extract useful tariff fields
                    duty = tariff_info.get("duty", "N/A")
                    footer_duty = tariff_info.get("footer_duty", "N/A")
                    new_tariff = tariff_info.get("New Tariff", "N/A")
                    ad = tariff_info.get("Anti-Dumping Duty", "N/A")
                    cvd = tariff_info.get("Countervailing Duty", "N/A")
                    total_duty = tariff_info.get("Total Duty", "N/A")
                    proposed_tariff = tariff_info.get("Proposed Tariff", "N/A")
                    # Format the data to match the expected structure
                    # Format final structure
                    transformed_item = {
                        "country": item.get("origin_country", "Unknown"),
                        "transaction_count": shipment_count,
                        "export_volume_tons": float(total_quantity) if total_quantity else 0,
                        "avg_price_per_ton": float(average_fob) if average_fob else 0,
                        "total_export_value": total_value,
                        "unit": item.get("global_std_unit_id", "KGS"),
                        "duty": duty,
                        "footer_duty": footer_duty,
                        "new_tariff": new_tariff,
                        "ad": ad,
                        "cvd": cvd,
                        "total_duty": total_duty,
                        "proposed_tariff": proposed_tariff
                    }
                    transformed_data.append(transformed_item)

                # If no data was found, return some default data for testing
                if not transformed_data:
                    return {
                        "status": "warning",
                        "message": "No supplier countries found for the given HS code.",
                        "data": []
                    }

                return {"status": "success", "data": transformed_data}
            else:
                # Return error from API
                error_message = api_response.get("error", "Unknown error from API")
                return {
                    "status": "error",
                    "message": error_message
                }
        else:
            # Return error if API call failed
            return {
                "status": "error",
                "message": f"API request failed with status code: {response.status_code}"
            }
    except Exception as e:
        # Handle any exceptions during API call
        return {
            "status": "error",
            "message": f"Failed to connect to API: {str(e)}"
        }

def api_get_top_countries(hs_code, chemical_name, destination, months=12):
    """Get top countries exporting a product with given HS code using the actual API.

    Args:
        hs_code (str): The HS code to search for
        chemical_name (str, optional): The name of the chemical
        months (int, optional): Number of months of data to retrieve

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import urllib.parse

    print("*****")
    print(hs_code, chemical_name, destination, months)
    print("*****")
    #exit(1)
    # Format HS code to required format (remove dots if present)
    formatted_hs_code = hs_code.replace(".", "")

    # Build API URL with query parameters
    base_url = "http://************:8080/api/top_suppliers_by_geography"

    # Prepare query parameters
    params = {
        "hs_code": formatted_hs_code,
        "destination": destination,
        "months": months
    }

    # Add chemical name if provided
    if chemical_name:
        params["chemical_name"] = chemical_name

    # Encode parameters for URL
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"

    try:
        # Make API request
        response = requests.get(api_url)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()

            # Check if API call was successful
            if api_response.get("success") is True and "result" in api_response:
                # Extract the data from result
                api_data = api_response["result"].get("data", [])

                # Transform API data to match expected structure in the application
                transformed_data = []

                for item in api_data:
                    # Handle potential missing or None values
                    shipment_count = item.get("shipment_count", 0) or 0
                    total_quantity = item.get("total_quantity", 0) or 0
                    average_fob = item.get("average_fob", 0) or 0

                    # Calculate total value based on average price and total quantity
                    total_value = average_fob * total_quantity if average_fob and total_quantity else 0

                    # Format the data to match the expected structure
                    transformed_item = {
                        "country": item.get("origin_country", "Unknown"),
                        "transaction_count": shipment_count,
                        "export_volume_tons": float(total_quantity) if total_quantity else 0,
                        "avg_price_per_ton": float(average_fob) if average_fob else 0,
                        "total_export_value": total_value,
                        "unit": item.get("global_std_unit_id", "KGS")
                    }

                    transformed_data.append(transformed_item)

                # If no data was found, return some default data for testing
                if not transformed_data:
                    return {
                        "status": "warning",
                        "message": "No supplier countries found for the given HS code.",
                        "data": []
                    }

                return {"status": "success", "data": transformed_data}
            else:
                # Return error from API
                error_message = api_response.get("error", "Unknown error from API")
                return {
                    "status": "error",
                    "message": error_message
                }
        else:
            # Return error if API call failed
            return {
                "status": "error",
                "message": f"API request failed with status code: {response.status_code}"
            }
    except Exception as e:
        # Handle any exceptions during API call
        return {
            "status": "error",
            "message": f"Failed to connect to API: {str(e)}"
        }

def api_get_top_suppliers(hs_code, country, months=12):
    """Get top suppliers from a specific country for a given HS code using the actual API.

    Args:
        hs_code (str): The HS code to search for
        country (str): The origin country to search for suppliers
        months (int, optional): Number of months of data to retrieve

    Returns:
        dict: A dictionary with status and data fields
    """
    import requests
    import urllib.parse

    # Format HS code to required format (remove dots if present)
    formatted_hs_code = hs_code.replace(".", "")

    # Build API URL with query parameters
    base_url = "http://************:8080/api/top_suppliers"

    # Prepare query parameters
    params = {
        "hs_code": formatted_hs_code,
        "origin": country,
        "months": months
    }

    # Encode parameters for URL
    query_string = urllib.parse.urlencode(params)
    api_url = f"{base_url}?{query_string}"

    try:
        # Make API request
        response = requests.get(api_url)

        # Check if request was successful
        if response.status_code == 200:
            # Parse JSON response
            api_response = response.json()

            # Check if API call was successful
            if api_response.get("success") is True and "result" in api_response:
                # Extract the data from result
                api_data = api_response["result"].get("data", [])

                # Transform API data to match expected structure in the application
                transformed_data = []

                for item in api_data:
                    # Handle potential missing or None values
                    shipment_count = item.get("shipment_count", 0) or 0
                    total_quantity = item.get("total_quantity", 0) or 0
                    average_fob = item.get("average_fob", 0) or 0

                    # Calculate total value based on average price and total quantity
                    total_export_value = average_fob * total_quantity if average_fob and total_quantity else 0

                    # Format the data to match the expected structure
                    transformed_item = {
                        "name": item.get("exporter_name", "Unknown Supplier"),
                        "global_exporter_id": item.get("global_exporter_id", ""),
                        "transaction_count": shipment_count,
                        "export_volume_tons": float(total_quantity) if total_quantity else 0,
                        "avg_price_per_ton": float(average_fob) if average_fob else 0,
                        "total_export_value": total_export_value,
                        "unit": item.get("global_std_unit_id", "KGS"),
                        "average_quantity_per_shipment": item.get("average_quantity_per_shipment", 0) or 0
                    }

                    transformed_data.append(transformed_item)

                # If no data was found, return an informative message
                if not transformed_data:
                    return {
                        "status": "warning",
                        "message": f"No suppliers found in {country} for the given HS code.",
                        "data": []
                    }

                return {"status": "success", "data": transformed_data}
            else:
                # Return error from API
                error_message = api_response.get("error", "Unknown error from API")
                return {
                    "status": "error",
                    "message": error_message
                }
        else:
            # Return error if API call failed
            return {
                "status": "error",
                "message": f"API request failed with status code: {response.status_code}"
            }
    except Exception as e:
        # Handle any exceptions during API call
        return {
            "status": "error",
            "message": f"Failed to connect to API: {str(e)}"
        }


# Helper functions for visualization
# def format_number(num):
#     """Format large numbers with commas."""
#     return f"{num:,}"
def format_number(num):
    """Format large numbers with commas and truncate to 2 decimal places."""
    if isinstance(num, float):
        num = int(num * 100) / 100  # Truncate to 2 decimal places
        return f"{num:,.2f}"
    return f"{num:,}"

# Commenting out chart functions that use pandas and plotly
"""
# def create_country_comparison_chart(countries_data):
#     # Create a bar chart comparing countries.
#     df = pd.DataFrame(countries_data)
#
#     fig = px.bar(
#         df,
#         x='country',
#         y='export_volume_tons',
#         color='country',
#         labels={'export_volume_tons': 'Export Volume (tons)', 'country': 'Country'},
#         title='Export Volume by Country',
#         text_auto=True
#     )
#
#     fig.update_layout(height=400)
#     return fig
#
# def create_supplier_comparison_chart(suppliers_data):
#     # Create a bar chart comparing suppliers.
#     df = pd.DataFrame(suppliers_data)
#
#     fig = px.bar(
#         df,
#         x='name',
#         y='export_volume_tons',
#         color='name',
#         labels={'export_volume_tons': 'Export Volume (tons)', 'name': 'Supplier'},
#         title='Export Volume by Supplier',
#         text_auto=True
#     )
#
#     fig.update_layout(height=400)
#     return fig
#
# def create_price_comparison_chart(data, x_field, name_field):
#     # Create a horizontal bar chart for price comparison.
#     df = pd.DataFrame(data)
#
#     fig = px.bar(
#         df,
#         y=name_field,
#         x='avg_price_per_ton',
#         orientation='h',
#         labels={'avg_price_per_ton': 'Avg. Price per Ton (USD)', name_field: x_field},
#         title=f'Average Price Comparison',
#         color='avg_price_per_ton',
#         color_continuous_scale='Viridis',
#         text_auto=True
#     )
#
#     fig.update_layout(height=300)
#     return fig
"""

# # Main app
# def main():
# Title section
#st.markdown("<div class='title-container'>", unsafe_allow_html=True)
st.markdown("""
    <style>
        .main .block-container {
            padding-top: 0rem;
            padding-bottom: 0rem;
            padding-left: 1rem;
            padding-right: 1rem;
        }
    </style>
""", unsafe_allow_html=True)
st.markdown("""
    <style>
        .main .block-container {
            padding: 0rem;
        }
        [data-testid="stSidebar"] {
            padding: 0rem;
        }
    </style>
""", unsafe_allow_html=True)
st.title("🧪 Chemical Supplier Finder")
st.markdown("Find the best suppliers for your chemical needs based on global trade data")
#st.markdown("</div>", unsafe_allow_html=True)

# Initialize session state variables if they don't exist
if 'step' not in st.session_state:
    st.session_state.step = 1
if 'hs_codes' not in st.session_state:
    st.session_state.hs_codes = None
if 'selected_hs_code' not in st.session_state:
    st.session_state.selected_hs_code = None
if 'countries_data' not in st.session_state:
    st.session_state.countries_data = None
if 'selected_country' not in st.session_state:
    st.session_state.selected_country = None
if 'suppliers_data' not in st.session_state:
    st.session_state.suppliers_data = None

if 'tariff' not in st.session_state:
    st.session_state.tariff = 0

use_tariff_country = 0
if use_tariff_country==1:
    st.session_state.tariff = 1

# Step 1: Enter chemical name and application
if st.session_state.step == 1:
    #st.markdown("<div class='card'>", unsafe_allow_html=True)
    st.subheader("Step 1: Enter Chemical Information")

    # col1, col2, col3 = st.columns(3)
    # with col1:
    #     chemical_name = st.text_input("Chemical Name", placeholder="e.g., EPOXIDE RESINS")
    # with col2:
    #     application = st.text_input("Application (Optional)", placeholder="e.g., coatings")
    # with col3:
    #     category = st.text_input("Category (Optional)", placeholder="e.g., Industrial")
    col1, col2, col3 = st.columns(3)
    with col1:
        chemical_name = st.text_input("Chemical Name", placeholder="e.g., EPOXIDE RESINS")
    with col2:
        application = st.text_input("Category", placeholder="e.g., Coatings")
    with col3:
        destination = st.selectbox("Destination", ['United States','China','India','Saudi Arabia','UAE','Indonesia','Mexico'])

    if destination == 'United States':
        st.session_state.tariff = 1
    else:
        st.session_state.tariff = 0

    st.session_state.destination = destination

    if st.button("Search HS Codes", type="primary", use_container_width=True):
        if chemical_name:
            st.session_state.chemical_name=chemical_name
            with st.spinner("Fetching HS codes..."):
                # Call API to get HS codes with all parameters
                response = api_get_hs_codes(chemical_name, application, application)
                #st.write(response)
                print(response["data"])
                if response["status"] == "success" and response["data"]:
                    print(response["data"])
                    st.session_state.hs_codes = response["data"][0]["hs_code"]
                    st.session_state.product_family = response["data"][0]["product_family"]
                    st.session_state.step = 2
                    st.rerun()
                else:
                    error_msg = response.get("message", "No HS codes found for the given chemical.")
                    st.error(f"{error_msg} Please try different parameters.")
        else:
            st.warning("Please enter a chemical name.")
    st.markdown("</div>", unsafe_allow_html=True)

# Step 2: Select HS code
elif st.session_state.step == 2:
    st.markdown("<div class='card'>", unsafe_allow_html=True)
    st.subheader("Step 2: Select HS Code")

    # options = {f"{code['hs_code'].split()[0]}": code['hs_code'].split()[0]
    #             for code in st.session_state.hs_codes}

    # selected_option = st.selectbox(
    #     "Select the most appropriate HS code for your chemical:",
    #     options.keys()
    # )

    # selected_hs_code = options[selected_option]

    # Create list of HS codes (just the code part)
    hs_code_list = []
    hs_code_map = {}

    for code in st.session_state.hs_codes:
        if isinstance(code, dict) and 'hs_code' in code and code['hs_code']:
            hs_code_raw = code['hs_code'].strip()

            if ' ' in hs_code_raw or ',' in hs_code_raw:
                hs_code_clean = hs_code_raw.replace(',', ' ').split()[0]
            else:
                hs_code_clean = hs_code_raw

            hs_code_list.append(hs_code_clean)
            hs_code_map[hs_code_clean] = hs_code_clean


    #shs_code_list = [code['hs_code'] for code in st.session_state.hs_codes]

    # Add placeholder for manual entry
    default_text = "Select or type an HS code"

    # Use st.selectbox with a free-text fallback
    selected_hs_code = st.selectbox(
        "Select or manually enter an HS code:",
        options=[default_text] + st.session_state.hs_codes,#hs_code_list,#[default_text] +
        index=0
    )

    # If user selects default, show text_input to enter manually
    if selected_hs_code == default_text:
        manual_entry = st.text_input("Enter HS code manually:")
        if manual_entry:
            selected_hs_code = manual_entry.strip()

    # Optional: validate or display selected code
    if selected_hs_code and selected_hs_code != default_text:
        st.success(f"Using HS Code: {selected_hs_code}")

    # #
    # Add placeholder for manual entry
    default_text_pf = "Select or type an Product Family"

    # Use st.selectbox with a free-text fallback
    selected_productFamily = st.selectbox(
        "Select or manually Product Family:",
        options=[default_text_pf] + st.session_state.product_family,#[default_text_pf] +
        index=0
    )

    # If user selects default, show text_input to enter manually
    if selected_productFamily == default_text_pf:
        manual_entry_pf = st.text_input("Enter Product Family dmanually:")
        if manual_entry_pf:
            selected_productFamily = manual_entry_pf.strip()
            st.session_state.selected_productFamily=selected_productFamily


    # Optional: validate or display selected code
    if selected_productFamily and selected_productFamily != default_text:
        st.success(f"Using Product Family: {selected_productFamily}")
        st.session_state.selected_productFamily=selected_productFamily

    col1, col2 = st.columns([1, 1])
    with col1:
        if st.button("← Back", use_container_width=True):
            st.session_state.step = 1
            st.rerun()

    with col2:
        if st.button("Find Top Exporting Countries", type="primary", use_container_width=True):

            if st.session_state.tariff == 0:
                # Call API to get top countries
                temp_chemical =st.session_state.chemical_name + "," + st.session_state.selected_productFamily
                temp_chemical =st.session_state.selected_productFamily
                st.write(temp_chemical)
                response = api_get_top_countries(selected_hs_code,temp_chemical,st.session_state.destination) #st.session_state.chemical_name)
            else:
                # Call API to get top countries
                temp_chemical = st.session_state.chemical_name + "," + st.session_state.selected_productFamily
                temp_chemical =st.session_state.selected_productFamily
                st.write(temp_chemical)
                response = api_get_top_countries_tariff(selected_hs_code,temp_chemical,st.session_state.destination)#st.session_state.chemical_name)

            if response["status"] == "success" and response["data"]:
                st.session_state.selected_hs_code = selected_hs_code
                st.session_state.countries_data = response["data"]
                st.session_state.step = 3
                st.rerun()
            else:
                st.error("No exporting countries found for the selected HS code.")
    st.markdown("</div>", unsafe_allow_html=True)

# Step 3: View and select top exporting countries
elif st.session_state.step == 3:
    st.markdown("<div class='card'>", unsafe_allow_html=True)
    st.subheader("Step 3: Top Exporting Countries")
    st.markdown(f"HS Code: **{st.session_state.selected_hs_code}**")

    # Get the chemical name from previous steps if available
    chemical_name = None
    if 'chemical_name' in st.session_state:
        chemical_name = st.session_state.chemical_name

    # Filter options
    col1, col2 = st.columns(2)
    with col1:
        months = st.slider("Months of Data", min_value=1, max_value=24, value=12,
                            help="Select the number of months of historical data to analyze")
    with col2:
        if st.button("Refresh Data", use_container_width=True):
            #Call API to get updated top countries

            with st.spinner("Fetching country data..."):
                if st.session_state.tariff == 0:
                    # Call API to get top countries
                    temp_chemical = st.session_state.chemical_name + "," + st.session_state.selected_productFamily
                    temp_chemical =st.session_state.selected_productFamily
                    st.write(temp_chemical)
                    response = api_get_top_countries( st.session_state.selected_hs_code,
                    temp_chemical,
                    st.session_state.destination,
                    months
                    )
                else:
                    # Call API to get top countries with taridd
                    temp_chemical = st.session_state.chemical_name + "," + st.session_state.selected_productFamily
                    temp_chemical =st.session_state.selected_productFamily
                    st.write(temp_chemical)                      #
                    response = api_get_top_countries_tariff(
                        st.session_state.selected_hs_code,
                        temp_chemical,
                        st.session_state.destination,
                        months
                    )

                if response["status"] == "success" and response["data"]:
                    st.session_state.countries_data = response["data"]
                    st.rerun()
                elif response["status"] == "warning":
                    st.warning(response["message"])
                else:
                    st.error(response["message"])

    # Check if we have data to display
    if not st.session_state.countries_data:
        st.info("No country data available. Try adjusting your filters or selecting a different HS code.")
    else:
        #st.write(st.session_state.countries_data)
        # Display country comparison charts if we have data
        # col1, col2 = st.columns([3, 2])

        # with col1:
        #     chart = create_country_comparison_chart(st.session_state.countries_data)
        #     st.plotly_chart(chart, use_container_width=True)

        # with col2:
        #     price_chart = create_price_comparison_chart(
        #         st.session_state.countries_data, 'Country', 'country')
        #     st.plotly_chart(price_chart, use_container_width=True)

        # # Display unit information if available
        # if 'unit' in st.session_state.countries_data[0]:
        #     unit = st.session_state.countries_data[0]['unit']
        #     st.info(f"Note: Quantities are measured in {unit}")

        # Display country data in cards
        st.markdown("### Select a Country")
        for country_data in st.session_state.countries_data:
            if st.session_state.tariff == 0:
                col1, col2, col3, col4, col5 = st.columns([2, 2, 2, 2, 2])

                with col1:
                    st.markdown(f"**{country_data['country']}**")
                with col2:
                    st.markdown(f"Transactions: {format_number(country_data['transaction_count'])}")
                with col3:
                    # Display with the right unit if available
                    unit_display = country_data.get('unit', 'tons')
                    st.markdown(f"Volume: {format_number(country_data['export_volume_tons'])} {unit_display}")
                with col4:
                    unit_price = f"${format_number(country_data['avg_price_per_ton'])}/{unit_display}"
                    st.markdown(f"Avg Price: {unit_price}")
                with col5:
                    if st.button(f"Select", key=f"country_{country_data['country']}"):
                        st.session_state.selected_country = country_data['country']
                        # Call API to get top suppliers
                        with st.spinner(f"Finding suppliers in {country_data['country']}..."):
                            response = api_get_top_suppliers(
                                st.session_state.selected_hs_code,
                                country_data['country']
                            )

                            if response["status"] == "success" and response["data"]:
                                st.session_state.suppliers_data = response["data"]
                                st.session_state.step = 4
                                st.rerun()
                            else:
                                error_msg = response.get("message", f"No suppliers found in {country_data['country']}.")
                                st.error(error_msg)
            else:
                col1, col2, col3, col4, col5,col6,col7,col8,col9 = st.columns([1,1.5,1.5,2,1.5,1,2,2,1.5])
                print(country_data)
                with col1:
                    st.markdown(f"**{country_data['country']}**")
                with col2:
                    st.markdown(f"#Records: {format_number(country_data['transaction_count'])}")
                with col3:
                    # Display with the right unit if available
                    unit_display = country_data.get('unit', 'tons')
                    st.markdown(f"Volume: {format_number(country_data['export_volume_tons'])} {unit_display}")
                with col4:
                    unit_price = f"${format_number(country_data['avg_price_per_ton'])}/{unit_display}"
                    st.markdown(f"Avg Price: {unit_price}")
                with col5:
                    duty = f"${country_data['duty']}"
                    st.markdown(f"Duty: {duty}")
                with col6:
                    footer_duty = f"${country_data['footer_duty']}"
                    st.markdown(f"Addtl Duty: {footer_duty}")
                with col7:
                    inplace_Trump = f"${country_data['new_tariff']}"
                    st.markdown(f"Tariff-InPlace: {inplace_Trump}")
                with col8:
                    proposed_Trump = f"${country_data['proposed_tariff']}"
                    st.markdown(f"Tariff-Proposed: {proposed_Trump}")
                with col9:
                    if st.button(f"Select", key=f"country_{country_data['country']}"):
                        st.session_state.selected_country = country_data['country']
                        # Call API to get top suppliers
                        with st.spinner(f"Finding suppliers in {country_data['country']}..."):
                            response = api_get_top_suppliers(
                                st.session_state.selected_hs_code,
                                country_data['country']
                            )

                            if response["status"] == "success" and response["data"]:
                                st.session_state.suppliers_data = response["data"]
                                st.session_state.step = 4
                                st.rerun()
                            else:
                                error_msg = response.get("message", f"No suppliers found in {country_data['country']}.")
                                st.error(error_msg)


    if st.button("← Back", use_container_width=True):
        st.session_state.step = 2
        st.rerun()
    st.markdown("</div>", unsafe_allow_html=True)

# Step 4: View top suppliers
elif st.session_state.step == 4:
    st.markdown("<div class='card'>", unsafe_allow_html=True)
    st.subheader("Step 4: Top Suppliers")
    st.markdown(f"HS Code: **{st.session_state.selected_hs_code}** | Country: **{st.session_state.selected_country}**")

    # Display the raw data instead of using pandas DataFrame
    st.title("Top Exporters by Volume")
    st.write(st.session_state.suppliers_data)
    # # Display supplier comparison charts
    # col1, col2 = st.columns([3, 2])

    # with col1:
    #     chart = create_supplier_comparison_chart(st.session_state.suppliers_data)
    #     st.plotly_chart(chart, use_container_width=True)

    # with col2:
    #     price_chart = create_price_comparison_chart(
    #         st.session_state.suppliers_data, 'Supplier', 'name')
    #     st.plotly_chart(price_chart, use_container_width=True)

    # Display supplier data in cards
    #st.markdown("### Supplier Details")
    # for supplier_data in st.session_state.suppliers_data:
    #     #st.markdown("<div class='card supplier-card'>", unsafe_allow_html=True)
    #     col1, col2 = st.columns([1, 3])

    #     with col1:
    #         st.markdown(f"### {supplier_data['name']}")
    #     with col2:
    #         col2_1, col2_2, col2_3,col2_4,col2_5 = st.columns(5)
    #         with col2_1:
    #             st.metric("Transaction Count", format_number(supplier_data['transaction_count']))
    #         with col2_2:
    #             st.metric("Eexport Volume Tons", f"{format_number(supplier_data['export_volume_tons'])}")
    #         with col2_3:
    #             st.metric("Avg. Price/ton", f"${format_number(supplier_data['avg_price_per_ton'])}")
    #         with col2_4:
    #             st.metric("Total Export Value", f"${format_number(supplier_data['total_export_value'])}")
    #         with col2_5:
    #             st.metric("Unit", f"{supplier_data['unit']}")



    #     st.markdown("</div>", unsafe_allow_html=True)
    #     st.markdown("<br>", unsafe_allow_html=True)

    if st.button("← Back to Countries", use_container_width=True):
        st.session_state.step = 3
        st.rerun()

    # Reset button
    if st.button("Start New Search", type="primary", use_container_width=True):
        for key in st.session_state.keys():
            del st.session_state[key]
        st.rerun()
    st.markdown("</div>", unsafe_allow_html=True)

# # Run the app
# if __name__ == "__main__":
#     main()
